#!/bin/bash

# 🚀 Script de Configuração Automática para Deploy no GCP
# Este script automatiza a configuração inicial do Google Cloud Platform

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Verificar se gcloud está instalado
check_gcloud() {
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI não está instalado. Instale em: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    print_message "gcloud CLI encontrado ✓"
}

# Verificar se gsutil está instalado
check_gsutil() {
    if ! command -v gsutil &> /dev/null; then
        print_error "gsutil não está instalado. Instale o Google Cloud SDK completo."
        exit 1
    fi
    print_message "gsutil encontrado ✓"
}

# Função principal
main() {
    echo "🚀 Configuração Automática do Deploy para GCP"
    echo "=============================================="
    echo

    # Verificar dependências
    print_step "Verificando dependências..."
    check_gcloud
    check_gsutil
    echo

    # Solicitar informações do usuário
    print_step "Coletando informações do projeto..."

    read -p "Digite o ID do seu projeto GCP: " PROJECT_ID
    read -p "Digite o nome do bucket de produção (ex: meu-site-prod): " PROD_BUCKET
    read -p "Digite o nome do bucket de preview (ex: meu-site-preview): " PREVIEW_BUCKET
    read -p "Digite a região para os buckets (ex: us-central1): " REGION
    read -p "Digite o repositório GitHub (formato: owner/repo): " GITHUB_REPO

    echo
    print_message "Configurações:"
    echo "  - Projeto: $PROJECT_ID"
    echo "  - Bucket Produção: $PROD_BUCKET"
    echo "  - Bucket Preview: $PREVIEW_BUCKET"
    echo "  - Região: $REGION"
    echo "  - Repositório: $GITHUB_REPO"
    echo

    read -p "Continuar com essas configurações? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Operação cancelada pelo usuário."
        exit 1
    fi

    # Configurar projeto e habilitar APIs
    print_step "Configurando projeto GCP e habilitando APIs..."
    gcloud config set project $PROJECT_ID

    # Habilitar APIs necessárias
    gcloud services enable iamcredentials.googleapis.com
    gcloud services enable storage.googleapis.com
    gcloud services enable sts.googleapis.com

    print_message "Projeto configurado e APIs habilitadas ✓"

    # Criar service account
    print_step "Criando service account..."
    gcloud iam service-accounts create github-actions \
        --description="Service account for GitHub Actions with Workload Identity" \
        --display-name="GitHub Actions Workload Identity" \
        --project=$PROJECT_ID || print_warning "Service account pode já existir"

    SERVICE_ACCOUNT_EMAIL="github-actions@${PROJECT_ID}.iam.gserviceaccount.com"
    print_message "Service account: $SERVICE_ACCOUNT_EMAIL ✓"

    # Conceder permissões
    print_step "Concedendo permissões à service account..."
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
        --role="roles/storage.admin"

    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
        --role="roles/storage.objectAdmin"

    print_message "Permissões concedidas ✓"

    # Criar Workload Identity Pool
    print_step "Criando Workload Identity Pool..."
    gcloud iam workload-identity-pools create "github-pool" \
        --project="$PROJECT_ID" \
        --location="global" \
        --display-name="GitHub Actions Pool" || print_warning "Pool pode já existir"

    WORKLOAD_IDENTITY_POOL_ID="projects/$PROJECT_ID/locations/global/workloadIdentityPools/github-pool"
    print_message "Pool ID: $WORKLOAD_IDENTITY_POOL_ID ✓"

    # Criar Workload Identity Provider
    print_step "Criando Workload Identity Provider..."
    gcloud iam workload-identity-pools providers create-oidc "github-provider" \
        --project="$PROJECT_ID" \
        --location="global" \
        --workload-identity-pool="github-pool" \
        --display-name="GitHub Actions Provider" \
        --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository,attribute.repository_owner=assertion.repository_owner" \
        --issuer-uri="https://token.actions.githubusercontent.com" || print_warning "Provider pode já existir"

    WORKLOAD_IDENTITY_PROVIDER="projects/$PROJECT_ID/locations/global/workloadIdentityPools/github-pool/providers/github-provider"
    print_message "Provider: $WORKLOAD_IDENTITY_PROVIDER ✓"

    # Configurar binding da service account
    print_step "Configurando binding da service account..."
    gcloud iam service-accounts add-iam-policy-binding \
        --role roles/iam.workloadIdentityUser \
        --member "principalSet://iam.googleapis.com/${WORKLOAD_IDENTITY_POOL_ID}/attribute.repository/${GITHUB_REPO}" \
        $SERVICE_ACCOUNT_EMAIL || print_warning "Binding pode já existir"

    print_message "Workload Identity configurado ✓"

    # Criar buckets
    print_step "Criando buckets do Cloud Storage..."

    # Bucket de produção
    gsutil mb -p $PROJECT_ID -c STANDARD -l $REGION gs://$PROD_BUCKET || print_warning "Bucket de produção pode já existir"
    gsutil web set -m index.html -e 404.html gs://$PROD_BUCKET
    gsutil iam ch allUsers:objectViewer gs://$PROD_BUCKET
    print_message "Bucket de produção configurado ✓"

    # Bucket de preview
    gsutil mb -p $PROJECT_ID -c STANDARD -l $REGION gs://$PREVIEW_BUCKET || print_warning "Bucket de preview pode já existir"
    gsutil web set -m index.html -e 404.html gs://$PREVIEW_BUCKET
    gsutil iam ch allUsers:objectViewer gs://$PREVIEW_BUCKET
    print_message "Bucket de preview configurado ✓"

    # Salvar informações em arquivo
    print_step "Salvando configurações..."
    cat > .env.deploy << EOF
# Configurações do Deploy GCP com Workload Identity
GCP_PROJECT_ID=$PROJECT_ID
GCS_BUCKET_NAME=$PROD_BUCKET
GCS_PREVIEW_BUCKET_NAME=$PREVIEW_BUCKET
REGION=$REGION
SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL
WORKLOAD_IDENTITY_PROVIDER=$WORKLOAD_IDENTITY_PROVIDER
GITHUB_REPO=$GITHUB_REPO
EOF

    print_message "Configurações salvas em .env.deploy ✓"

    # Instruções finais
    echo
    print_step "🎉 Configuração com Workload Identity concluída com sucesso!"
    echo
    print_message "Próximos passos:"
    echo "1. Vá para Settings > Secrets and variables > Actions no seu repositório GitHub"
    echo "2. Adicione os seguintes secrets:"
    echo "   - GCP_PROJECT_ID: $PROJECT_ID"
    echo "   - GCS_BUCKET_NAME: $PROD_BUCKET"
    echo "   - GCS_PREVIEW_BUCKET_NAME: $PREVIEW_BUCKET"
    echo "   - WIF_PROVIDER: $WORKLOAD_IDENTITY_PROVIDER"
    echo "   - WIF_SERVICE_ACCOUNT: $SERVICE_ACCOUNT_EMAIL"
    echo
    print_message "URLs de acesso:"
    echo "   - Produção: https://storage.googleapis.com/$PROD_BUCKET/index.html"
    echo "   - Preview: https://storage.googleapis.com/$PREVIEW_BUCKET/pr-{PR_NUMBER}/index.html"
    echo
    print_message "✅ Vantagens do Workload Identity:"
    echo "   - Sem chaves JSON para gerenciar"
    echo "   - Rotação automática de credenciais"
    echo "   - Auditoria completa de acesso"
    echo "   - Mais seguro que chaves estáticas"
    echo
    print_warning "IMPORTANTE: Verifique se o repositório GitHub está correto: $GITHUB_REPO"
}

# Executar função principal
main "$@"
