interface GenerateQrCodeInputInterface {
    instanceName: string;
}

interface GetStatusInstanceInputInterface extends GenerateQrCodeInputInterface {}

interface SendMessageInputInterface extends GetStatusInstanceInputInterface {
    delay: number;
    number: string;
    text: string;
}

interface CheckConnectionStatusOutputInterface {
    success: boolean;
    isConnected: boolean;
    error: string | null;
    rawResponse: string;
}

interface GenerateQrCodeOutputInterface {
    success?: boolean;
    base64?: string | null;
    code?: string | null;
    error?: string | null;
    rawResponse?: string;
    message?: string | null;
    status?: string | null;
    connected?: boolean | null;
    pairingCode?: string | null;
}

export type {
    CheckConnectionStatusOutputInterface, GenerateQrCodeInputInterface, GenerateQrCodeOutputInterface, GetStatusInstanceInputInterface,
    SendMessageInputInterface
};

