
export interface CreateAgentInputInterface {
    context: string; // default: "um assistente"
    name: string; // default: "Bender"
    userId: number; // default: 2
    agentParamsId?: number | null; // default: 2
}

export interface EditAgentInputInterface {
    context: string;
    name: string;
    agentParamsId?: number | null;
}
export interface SearchAgentInputInterface {
    ParamName: string;
    userId: number;
}


export interface SearchAgentOutputInterface {
    id: number;
    paramName: string;
    companyName: string | null;
    companyDescription: string | null;
    tone: string | null;
    goals: string | null;
    mainMission: string | null;
    contextDescription: string | null;
    qualificationRules: string | null;
    conversationGuidelines: string | null;
    openingScript: string | null;
    preQualificationQuestions: string | null;
    painAgitationScript: string | null;
    pricingAgitationScript: string | null;
    traditionalMethods: string | null;
    solutionScript: string | null;
    valueGenerationScript: string | null;
    finalQualificationQuestions: string | null;
    opportunityReinforcementScript: string | null;
    emotionalActivationScript: string | null;
    callToActionScript: string | null;
    disqualifiedFlowScript: string | null;
    restrictionsAndLimits: string | null;
    askAvailabilityStyle: string | null;
    confirmationStyle: string | null;
    userTone: string | null;
    alternativeSuggestionStyle: string | null;
    reminderStyle: string | null;
    reminderTiming: string | null;
    recurrenceStyle: string | null;
    callToAction: string | null;
    courtesyMessage: string | null;
    userId: number;
    user: string | null;
}
export interface IAgent {
    id: number;
    name: string;
    context: string;
    userId: number;
    agentParamsId: number | null;
    agentParams: IAgentParams | null;
    instanceWhatsappName: string | null;
    messages: string;
    user: string;
    paramName: string | null;
}

export interface IAgentParams {
    id: number;
    paramName: string;
    companyName: string | null;
    companyDescription: string | null;
    tone: string | null;
    goals: string | null;
    mainMission: string | null;
    contextDescription: string | null;
    qualificationRules: string | null;
    conversationGuidelines: string | null;
    openingScript: string | null;
    preQualificationQuestions: string | null;
    painAgitationScript: string | null;
    pricingAgitationScript: string | null;
    traditionalMethods: string | null;
    solutionScript: string | null;
    valueGenerationScript: string | null;
    finalQualificationQuestions: string | null;
    opportunityReinforcementScript: string | null;
    emotionalActivationScript: string | null;
    callToActionScript: string | null;
    disqualifiedFlowScript: string | null;
    restrictionsAndLimits: string | null;
    askAvailabilityStyle: string | null;
    confirmationStyle: string | null;
    userTone: string | null;
    alternativeSuggestionStyle: string | null;
    reminderStyle: string | null;
    reminderTiming: string | null;
    recurrenceStyle: string | null;
    callToAction: string | null;
    courtesyMessage: string | null;
    userId: number;
    user: string | null;

}

export type IAgentParamsWithoutId = Omit<IAgentParams, 'id'>;

