import type {
  CreateAgentInputInterface,
  EditAgentInputInterface,
  IAgent,
  SearchAgentInputInterface,
  SearchAgentOutputInterface,
} from '@/interfaces/IAgents';
import { createAgentInputs } from '@/mocks/agents/Inputs';
import {
  CreateAgentSchema,
  type CreateAgentInput,
} from '@/schemas/agents-schema';
import { useAgentsParamsService } from '@/services/agents/agents-params.service';
import { useAgentService } from '@/services/agents/agents.service';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

interface openDialogProps {
  createDialogIsOpen?: boolean;
  editDialogIsOpen?: boolean;
}

function useAgents() {
  const [loading, setLoading] = useState(false);
  const [agents, setAgents] = useState<IAgent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<IAgent | null>(
    null as IAgent | null
  );
  const [tabs, setTabs] = useState<string>('list');
  const [dialogIsOpen, setDialogIsOpen] = useState<openDialogProps>({
    createDialogIsOpen: false,
    editDialogIsOpen: false,
  });
  const [dialogType, setDialogType] = useState<'create' | 'edit'>('create');
  const [dialogSearchIsOpen, setDialogSearchIsOpen] = useState<boolean>(false);
  const [initialSearchLabel, setInitialSearchLabel] = useState<
    string | undefined
  >(undefined);

  const form = useForm<CreateAgentInput>({
    resolver: zodResolver(CreateAgentSchema),
    defaultValues: {
      name: '',
      context: '',
    },
  });

  const editForm = useForm<CreateAgentInput>({
    resolver: zodResolver(CreateAgentSchema),
    defaultValues: {
      name: selectedAgent?.name || '',
      context: selectedAgent?.context || '',
    },
  });

  const handleSearchAgentByName = async (name: string) => {
    try {
      setLoading(true);
      const params: SearchAgentInputInterface = {
        ParamName: name,
        userId: 2,
      };
      return await useAgentsParamsService.searchByName(params);
    } catch (error) {
      console.error('Error searching agent params by name:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleGetAll = async (userId: number) => {
    try {
      setLoading(true);
      const agents = await useAgentService.getAll(userId);
      setAgents(agents);
    } catch (error) {
      console.error('Erro ao buscar agentes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditAgent = async (agent: IAgent) => {
    editForm.reset();
    setSelectedAgent(agent);
    setDialogType('edit');

    const paramsAgentById = await handleGetParamsById(
      agent.agentParamsId as number
    );


    setInitialSearchLabel(paramsAgentById?.paramName);
    editForm.setValue('name', agent.name);
    editForm.setValue('context', agent.context || '');
    editForm.setValue('agentParamsId', paramsAgentById?.id || undefined);

    setDialogIsOpen({ ...dialogIsOpen, editDialogIsOpen: true });
  };

  const inputs = [
    ...createAgentInputs,
    {
      name: 'agentParamsId',
      label: 'Buscar Parâmetro',
      placeholder: 'Digite o nome do parâmetro',
      type: 'search',
      searchFn: handleSearchAgentByName,
      getLabel: (item: SearchAgentOutputInterface) => item.paramName,
      debounceMs: 300,
    },
  ];

  const handleSelectAgent = (agent: IAgent) => {
    setSelectedAgent(agent);
    sessionStorage.setItem('@highSelectedAgent', JSON.stringify(agent));
    setDialogSearchIsOpen(!dialogSearchIsOpen);
  };

  function handleChangeTab(value: string) {
    setTabs(value);
  }

  const handleGetParamsById = async (id: number) => {
    try {
      if (!id) return;
      setLoading(true);
      const response = await useAgentsParamsService.getById(id);
      return response;
    } catch (error) {
      console.error('Error fetching agent params by ID:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (data: CreateAgentInput) => {
    try {
      setLoading(true);
      const params: CreateAgentInputInterface = {
        name: data.name,
        context: data.context || 'um assistente',
        userId: 2,
        agentParamsId: data.agentParamsId,
      };
      const response = await useAgentService.create(params);
      setDialogIsOpen({ ...dialogIsOpen, createDialogIsOpen: false });
      setAgents((prevAgents) => [...prevAgents, response.value]);
      setTabs('list');
      toast('Agente criado com sucesso!', {
        description: 'Novo agente adicionado à sua lista.',
      });
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      form.reset();
      setLoading(false);
    }
  };

  const editHandleSubmit = async (data: CreateAgentInput) => {
    try {
      setLoading(true);
      if (!selectedAgent) return;
      const params: EditAgentInputInterface = {
        name: data.name,
        context: data.context || 'um assistente',
        agentParamsId: data.agentParamsId || null,
      };
      const updatedAgent = await useAgentService.update(
        selectedAgent.id,
        params
      );
      setAgents((prevAgents) =>
        prevAgents.map((a) =>
          a.id === selectedAgent.id ? { ...a, ...updatedAgent.value } : a
        )
      );
      setDialogIsOpen({ ...dialogIsOpen, editDialogIsOpen: false });
      setTabs('list');
      toast('Agente atualizado com sucesso!', {
        description: 'As informações do agente foram atualizadas.',
      });
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      editForm.reset();
      setLoading(false);
    }
  };

  const handleDelete = async (agentId: number) => {
    try {
      setLoading(true);
      await useAgentService.delete(agentId);
      setAgents(agents.filter((agent) => agent.id !== agentId));
      toast('Agente deletado com sucesso!', {
        description: 'O agente foi removido da sua lista.',
      });
    } catch (error) {
      console.error('Error deleting agent:', error);
    } finally {
      setLoading(false);
    }
  };

  //useEffects
  useEffect(() => {
    handleGetAll(2);
  }, []);

  return {
    form,
    inputs,
    handleSubmit,
    loading,
    agents,
    handleSelectAgent,
    handleGetAll,
    tabs,
    handleChangeTab,
    selectedAgent,
    editForm,
    handleEditAgent,
    dialogIsOpen,
    setDialogIsOpen,
    dialogType,
    editHandleSubmit,
    handleDelete,
    dialogSearchIsOpen,
    setDialogSearchIsOpen,
    handleSearchAgentByName,
    initialSearchLabel
  };
}

export { useAgents };
