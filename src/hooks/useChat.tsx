import type { IAgent } from '@/interfaces/IAgents';
import { useAgentService } from '@/services/agents/agents.service';
import { useChatService } from '@/services/chat/chat.service';
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

function useChat() {
  const [loading, setLoading] = useState(false);
  const [agents, setAgents] = useState<IAgent[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [qrCodeUrlImage, setQrCodeUrlImage] = useState<
    string | null | undefined
  >(null);
  const [selectedAgent, setSelectedAgent] = useState<IAgent | null>(null);

  const navigate = useNavigate();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const handleGetAgentsByUserId = async (userId: number) => {
    try {
      setLoading(true);
      const agents = await useAgentService.getAll(userId);
      setAgents(agents);
    } catch (error) {
      console.error('Erro ao buscar agentes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleModal = (open: boolean) => {
    setDialogOpen(open);
  };

  const handleCheckInstanceStatus = async (
    agent: IAgent,
    showLoading = true,
    polling = false
  ) => {
    try {
      if (!agent.instanceWhatsappName) {
        toast.error('Nome da instância inválido.');
        return;
      }

      if (showLoading) setLoading(true);

      const status = await useChatService.getStatusInstance({
        instanceName: agent.instanceWhatsappName,
      });
      if (!status.isConnected && !polling) {
        return handleGenerateQrCode(agent.instanceWhatsappName);
      }
      navigate(`conversation/${agent.id}`);
    } catch (error) {
      console.error('Erro ao verificar status da instância:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAgent = (agent: IAgent) => {
    setSelectedAgent(agent);
    handleCheckInstanceStatus(agent, true, false);
  };

  const handleGenerateQrCode = async (instanceName: string) => {
    try {
      const response = await useChatService.generateQrCode({ instanceName });
      console.log('QR Code gerado:', response);
      setDialogOpen(true);
      setQrCodeUrlImage(response.base64);
    } catch (error) {
      console.error('Erro ao gerar QR Code:', error);
      toast.error('Erro ao gerar QR Code. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const userId = 2; // TODO: alterar pelo id de usuário logado
    handleGetAgentsByUserId(userId);
  }, []);

  useEffect(() => {
    if (dialogOpen && selectedAgent) {
      intervalRef.current = setInterval(() => {
        handleCheckInstanceStatus(selectedAgent, false, true);
      }, 5000);
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [dialogOpen, selectedAgent]);

  return {
    loading,
    agents,
    handleSelectAgent,
    dialogOpen,
    handleModal,
    qrCodeUrlImage,
  };
}

export default useChat;
