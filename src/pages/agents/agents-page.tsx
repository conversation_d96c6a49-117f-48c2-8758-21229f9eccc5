import { Button } from '@/components';
import { AgentCardList } from '@/components/organisms/agents/agent-card';
import { AgentDialog } from '@/components/organisms/agents/agent-dialog';
import { AgentParamsCard } from '@/components/organisms/agents/agent-params-card';
import { CustomForm } from '@/components/organisms/custom-form';
import LoaderComponent from '@/components/organisms/loader';
import PageHeader from '@/components/organisms/page-header';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAgents } from '@/hooks/useAgents';
import useAgentsParams from '@/hooks/useAgentsParams';
import { paramsAgentInputs } from '@/mocks/agents/Inputs';
import type { CreateAgentParamsInput } from '@/schemas/agents-schema';
import { ListCollapse, ListPlus, RefreshCcw, SquarePen } from 'lucide-react';
import { RiRobot2Fill } from 'react-icons/ri';

const AgentsPage = () => {
  const {
    form,
    inputs,
    handleSubmit,
    loading,
    agents,
    handleSelectAgent,
    handleGetAll,
    tabs,
    handleChangeTab,
    handleEditAgent,
    dialogIsOpen,
    setDialogIsOpen,
    editHandleSubmit,
    handleDelete,
    editForm,
    dialogSearchIsOpen,
    setDialogSearchIsOpen,
    initialSearchLabel
  } = useAgents();

  const {
    paramsForm,
    handleCreateAgentParams,
    loadingParams,
    agentParams,
    handleEditAgentParams,
    handleChangeTabParam,
    tabsParams,
    handleSelectParam,
    selectedAgentParam,
    editParamsForm,
    handleDeleteParams,
    handleGetParams
  } = useAgentsParams();

  return (
    <main className="container mx-auto flex flex-col">
      <PageHeader
        title="Agentes"
        description="Gerencie seus agentes de forma eficaz."
        icon={<RiRobot2Fill size={30} className="text-blue-500" />}
      />
      {loading && <LoaderComponent />}
      {loadingParams && <LoaderComponent />}
      <Tabs
        defaultValue="list"
        value={tabs}
        className="w-full"
        onValueChange={handleChangeTab}
      >
        <TabsList className="mb-4 flex gap-4 bg-gray-100 rounded-lg p-2 shadow h-12">
          <TabsTrigger
            value="list"
            className="flex-1 py-3 px-6 text-md font-medium rounded-lg transition-colors cursor-pointer data-[state=active]:bg-gray-700 data-[state=active]:text-white bg-white text-gray-700 hover:bg-gray-100"
          >
            Listagem
          </TabsTrigger>
          <TabsTrigger
            value="params"
            className="flex-1 py-3 px-6 text-md font-medium rounded-lg transition-colors cursor-pointer data-[state=active]:bg-gray-700 data-[state=active]:text-white bg-white text-gray-700 hover:bg-gray-100"
          >
            Parâmetros
          </TabsTrigger>
        </TabsList>
        <TabsContent value="list">
          <div className="flex flex-col gap-4 pt-6">
            <section className="flex self-end gap-4 items-center">
              <Button
                onClick={() => handleGetAll(2)}
                className="w-fit flex items-center gap-2 font-semibold h-10 px-4 py-2 rounded shadow transition-colors cursor-pointer bg-white text-gray-700 hover:bg-gray-100"
              >
                <RefreshCcw size={20} />
                Atualizar
              </Button>
              <AgentDialog
                form={form}
                inputs={inputs}
                handleSubmit={handleSubmit}
                type="create"
                dialogIsOpen={dialogIsOpen.createDialogIsOpen}
                setDialogIsOpen={() =>
                  setDialogIsOpen({
                    ...dialogIsOpen,
                    createDialogIsOpen: !dialogIsOpen.createDialogIsOpen,
                  })
                }
                key={'create-agent-dialog'}
              />
            </section>
            <AgentCardList
              agents={agents}
              onSelect={(v) => handleSelectAgent(v)}
              onEdit={(v) => handleEditAgent(v)}
              onDelete={(v) => handleDelete(v.id)}
            />
            <AgentDialog
              form={editForm}
              inputs={inputs}
              handleSubmit={editHandleSubmit}
              type="edit"
              dialogIsOpen={dialogIsOpen.editDialogIsOpen}
              setDialogIsOpen={() =>
                setDialogIsOpen({ ...dialogIsOpen, editDialogIsOpen: false })
              }
              key={'edit-agent-dialog'}
              initialSearchLabel={initialSearchLabel}
            />
          </div>
        </TabsContent>
        {/* params */}
        <TabsContent value="params" className="relative">
          <Tabs
            defaultValue="list-params"
            className="w-full"
            value={tabsParams}
            onValueChange={handleChangeTabParam}
          >
            <Button
              onClick={() => handleGetParams(2)}
              className="w-fit flex items-center absolute top-4 left-4 gap-2 font-semibold h-10 px-4 py-2 rounded shadow transition-colors cursor-pointer bg-white text-gray-700 hover:bg-gray-100"
            >
              <RefreshCcw size={20} />
              Atualizar
            </Button>
            <TabsList className="mb-4 flex gap-4 bg-gray-50 rounded-lg p-2 shadow h-12 self-end">
              <TabsTrigger value="list-params" className="cursor-pointer ">
                <ListCollapse size={20} />
                <span>Todos</span>
              </TabsTrigger>
              <TabsTrigger
                disabled={!selectedAgentParam?.id}
                value="edit-params"
                className={`cursor-pointer`}
              >
                <SquarePen size={20} />
                <span>Editar</span>
              </TabsTrigger>
              <TabsTrigger value="create-params" className="cursor-pointer ">
                <ListPlus size={20} />
                <span>Novo</span>
              </TabsTrigger>
            </TabsList>
            <TabsContent value="list-params">
              <AgentParamsCard
                agentParams={agentParams}
                onEdit={handleSelectParam}
                onDelete={(param) => handleDeleteParams(param.id)}
              />
            </TabsContent>
            <TabsContent value="edit-params">
              {selectedAgentParam?.id && (
                <ScrollArea className="h-[500px] w-full p-2">
                  <CustomForm<CreateAgentParamsInput>
                    form={editParamsForm}
                    onSubmit={(data) =>
                      handleEditAgentParams(selectedAgentParam.id, data)
                    }
                    fields={paramsAgentInputs}
                    formClassName="grid grid-cols-1 md:grid-cols-2 gap-4"
                  />
                </ScrollArea>
              )}
            </TabsContent>
            <TabsContent value="create-params">
              <ScrollArea className="h-[500px] w-full p-2">
                <CustomForm<CreateAgentParamsInput>
                  form={paramsForm}
                  onSubmit={(data) => handleCreateAgentParams(data)}
                  fields={paramsAgentInputs}
                  formClassName="grid grid-cols-1 md:grid-cols-2 gap-4"
                />
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </TabsContent>
      </Tabs>
    </main>
  );
};

export default AgentsPage;
