import { Button } from '@/components';
import PageHeader from '@/components/organisms/page-header';
import { ArrowLeftIcon, ChartGanttIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

function ConversationChatPage() {
    const navigate = useNavigate();
  return (
    <main className="container mx-auto flex flex-col">
      <PageHeader
        title="Conversa"
        description="Gerencie suas conversas de forma eficaz."
        icon={<ChartGanttIcon size={30} className="text-blue-500" />}
      />
      <section>
        <Button className="p-4 mt-4 flex items-center cursor-pointer hover:bg-gray-50" variant="secondary" onClick={() => navigate(-1)}>
          <ArrowLeftIcon className="inline-block" />
          <h2 className="text-sm font-semibold">Voltar</h2>
        </Button>
      </section>
    </main>
  );
}
export default ConversationChatPage;
