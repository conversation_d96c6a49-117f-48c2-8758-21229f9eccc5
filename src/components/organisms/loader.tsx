
type LoaderType = 'spinner' | 'dots' | 'bars';

interface LoaderProps {
    title?: string;
    type?: LoaderType;
}

const LoaderComponent = ({ title, type = 'spinner' }: LoaderProps) => {
    return (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '100%',
                width: '100%',
                position: 'absolute',
                top: 0,
                left: 0,
                background: 'rgba(255,255,255,0.8)',
                zIndex: 9999,
            }}
            data-testid="custom-loader"
        >
            {title && (
                <div style={{ marginBottom: 24, fontSize: 20, fontWeight: 500 }}>
                    {title}
                </div>
            )}
            {type === 'spinner' && (
                <div style={{ width: 64, height: 64, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <div
                        style={{
                            border: '8px solid #eee',
                            borderTop: '8px solid #1976d2',
                            borderRadius: '50%',
                            width: 48,
                            height: 48,
                            animation: 'spin 1s linear infinite',
                        }}
                    />
                    <style>
                        {`
                            @keyframes spin {
                                0% { transform: rotate(0deg);}
                                100% { transform: rotate(360deg);}
                            }
                        `}
                    </style>
                </div>
            )}
            {type === 'dots' && (
                <div style={{ display: 'flex', gap: 8 }}>
                    {[0, 1, 2].map(i => (
                        <div
                            key={i}
                            style={{
                                width: 16,
                                height: 16,
                                borderRadius: '50%',
                                background: '#1976d2',
                                animation: `dotBounce 1s infinite ${i * 0.2}s`,
                            }}
                        />
                    ))}
                    <style>
                        {`
                            @keyframes dotBounce {
                                0%, 80%, 100% { transform: translateY(0);}
                                40% { transform: translateY(-16px);}
                            }
                        `}
                    </style>
                </div>
            )}
            {type === 'bars' && (
                <div style={{ display: 'flex', gap: 6, alignItems: 'flex-end', height: 32 }}>
                    {[0, 1, 2, 3].map(i => (
                        <div
                            key={i}
                            style={{
                                width: 8,
                                height: 24,
                                background: '#1976d2',
                                borderRadius: 4,
                                animation: `barGrow 1s infinite ${i * 0.15}s`,
                            }}
                        />
                    ))}
                    <style>
                        {`
                            @keyframes barGrow {
                                0%, 100% { height: 24px;}
                                50% { height: 32px;}
                            }
                        `}
                    </style>
                </div>
            )}
        </div>
    );
};

export default LoaderComponent;