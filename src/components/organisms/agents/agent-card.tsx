import { Button } from '@/components/atoms';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { IAgent } from '@/interfaces/IAgents';
import { sliceLargeString } from '@/utils/sliceLargeString';
import { Bot, Cog, Edit, ScrollText, Trash } from 'lucide-react'; // Ícone de redirecionamento
import NotFoundData from '../not-found-data';

interface AgentCardListProps {
  agents: IAgent[];
  onSelect?: (agent: IAgent) => void;
  onEdit?: (agent: IAgent) => void;
  onDelete?: (agent: IAgent) => void;
  showActions?: boolean;
}

export function AgentCardList({
  agents,
  onEdit,
  onDelete,
  showActions = true,
  onSelect
}: AgentCardListProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      {agents.length > 0 ? (
        agents?.map((agent) => (
          <Card
            key={agent.id}
            className="transition-transform duration-200 hover:shadow-lg relative cursor-pointer"
            onClick={() => onSelect?.(agent)}
          >
            <CardHeader className="flex items-center justify-between">
              <div className='flex items-center gap-2'>
                <Bot color='blue' size={20}/>
                <h3 className="text-lg font-semibold">{agent.name}</h3>
              </div>
              {showActions && (
                <div className="flex items-center gap-2 flex-col">
                  <Tooltip>
                    <TooltipTrigger>
                      <Button
                        className="bg-transparent border border-gray-300 hover:bg-gray-100 cursor-pointer hover:scale-105 transition-transform"
                        onClick={() => onEdit?.(agent)}
                      >
                        <Edit className=" text-yellow-500" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Editar agente</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger>
                      <Button
                        className="bg-transparent border border-gray-300 hover:bg-gray-100 cursor-pointer hover:scale-105 transition-transform"
                        onClick={() => onDelete?.(agent)}
                      >
                        <Trash className="text-red-500" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Excluir Agente</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              ) }
            </CardHeader>
            <CardContent className="flex flex-col gap-2">
              <p className="text-muted-foreground flex items-center gap-1">
                <ScrollText size={20} /> Descrição:{' '}
                {sliceLargeString(agent.context)}
              </p>

              <p className="text-muted-foreground flex items-center gap-1">
                <Cog size={20} />
                Parâmetro: {sliceLargeString(agent.paramName || 'Não definido')}
              </p>
            </CardContent>
          </Card>
        ))
      ) : (
        <div className="col-span-full">
          <NotFoundData />
        </div>
      )}
    </div>
  );
}
