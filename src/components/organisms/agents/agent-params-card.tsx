import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { IAgentParams } from '@/interfaces/IAgents';
import { Building2, Edit, Trash } from 'lucide-react';
import NotFoundData from '../not-found-data';

interface Props {
  agentParams: IAgentParams[] | null;
  onEdit?: (param: IAgentParams) => void;
  onDelete?: (param: IAgentParams) => void;
}

export function AgentParamsCard({ agentParams, onEdit, onDelete }: Props) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      {agentParams && agentParams.length > 0 ? (
        agentParams.map((param, index) => (
          <Card
            key={index}
            className="transition-transform duration-200 hover:scale-105 hover:shadow-lg relative"
          >
            <CardHeader className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                {param.paramName || 'Parâmetro sem nome'}
              </h3>
              <div className="flex items-center gap-2 flex-col">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="border border-gray-300 hover:bg-gray-100"
                      onClick={() => onEdit?.(param)}
                    >
                      <Edit className="text-yellow-500" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Editar Parâmetro</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="border border-gray-300 hover:bg-gray-100"
                      onClick={() => onDelete?.(param)}
                    >
                      <Trash className="text-red-500" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Excluir Parâmetro</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground flex items-center gap-1 mb-2">
                <Building2 size={20} /> {param.companyName || 'Sem nome da empresa'}
              </p>
            </CardContent>
          </Card>
        ))
      ) : (
        <div className="col-span-full">
          <NotFoundData />
        </div>
      )}
    </div>
  );
}
