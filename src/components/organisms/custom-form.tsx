/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from '@/components/ui/button';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import type { CustomFormProps } from '@/interfaces/ICustomForm';
import type { FieldValues } from 'node_modules/react-hook-form/dist/types/fields';
import { Search } from './search';

export function CustomForm<T extends FieldValues = FieldValues>({
  fields,
  onSubmit,
  submitLabel = 'Enviar',
  form,
  formClassName,
  initialSearchLabel
}: CustomFormProps<T>) {
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={`space-y-4 ${formClassName}`}
      >
        {fields.map((field) => (
          <FormField
            key={field.name}
            control={form.control}
            name={field.name as any}
            render={({ field: rhfField }) => (
              <FormItem className={field.wrapperClassName}>
                <FormLabel>{field.label}</FormLabel>

                {field.type === 'text' ? (
                  <Input
                    placeholder={field.placeholder}
                    {...rhfField}
                    className={field.inputClassName}
                  />
                ) : field.type === 'number' ? (
                  <Input
                    type="number"
                    placeholder={field.placeholder}
                    {...rhfField}
                    onChange={(e) => rhfField.onChange(Number(e.target.value))}
                    className={field.inputClassName}
                  />
                ) : field.type === 'textarea' ? (
                  <Textarea
                    placeholder={field.placeholder}
                    {...rhfField}
                    className={`${field.inputClassName} max-h-40`}
                  />
                ) : field.type === 'select' ? (
                  <Select
                    onValueChange={rhfField.onChange}
                    value={rhfField.value}
                  >
                    <SelectTrigger className={field.inputClassName}>
                      <SelectValue placeholder={field.placeholder} />
                    </SelectTrigger>
                    <SelectContent>
                      {field.options?.map((opt) => (
                        <SelectItem key={opt.value} value={opt.value}>
                          {opt.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : field.type === 'search' ? (
                  <Search<any>
                    searchFn={field.searchFn!}
                    getLabel={field.getLabel!}
                    onSelect={(item) => {
                      console.log(item.id);
                      rhfField.onChange(item.id);
                    }}
                    debounceMs={field.debounceMs}
                    initialLabel={initialSearchLabel}
                  />
                ) : null}

                <FormMessage />
              </FormItem>
            )}
          />
        ))}

        <Button type="submit" className="w-full">
          {submitLabel}
        </Button>
      </form>
    </Form>
  );
}
