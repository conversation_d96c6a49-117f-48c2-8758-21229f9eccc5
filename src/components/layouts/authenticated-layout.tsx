import { Outlet } from 'react-router-dom';
import { Sidebar } from '../organisms';
import { SidebarItem } from '../organisms/sidebar';
import { SIDEBAR_ITEMS } from '@/sidebar-config';

interface AuthenticatedLayoutProps {
  className?: string;
}

export function AuthenticatedLayout({ className }: AuthenticatedLayoutProps) {

  return (
    <div className={`w-full min-h-screen ${className || ''}`}>
      <main className="w-full flex">
        <Sidebar>
          {SIDEBAR_ITEMS.map((item, index) => (
            <SidebarItem key={index} text={item.text} icon={item.icon} active={item.to === location.pathname} to={item.to} alert={false} />
          ))}
        </Sidebar>
        <div className="py-16 px-8 flex-1 overflow-y-auto">
          <Outlet />
        </div>
      </main>
    </div>
  );
}
